<!-- 本月目标 -->
<template>
  <ele-card class="statistics-card" :header="title">
    <template #extra>
      <more-icon @command="onCommand" />
    </template>
    <ele-text size="xxl" class="statistics-value">8,846</ele-text>
    <v-chart
      ref="visitChartRef"
      :option="visitChartOption"
      style="height: 36px"
    />
    <el-divider />
    <div>日访问量 1,234</div>
  </ele-card>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import MoreIcon from './more-icon.vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { use } from 'echarts/core';
  import type { EChartsCoreOption } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart, BarChart } from 'echarts/charts';
  import { GridComponent, TooltipComponent } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getPayNumList } from '@/api/demo';
  import { useEcharts } from '@/utils/use-echarts';
  defineProps({
    title: String
  });

  const emit = defineEmits(['command']);

  const onCommand = (command) => {
    emit('command', command);
  };
  use([CanvasRenderer, LineChart, BarChart, GridComponent, TooltipComponent]);

  /** 访问量图表 */
  const visitChartRef = ref<InstanceType<typeof VChart> | null>(null);

  /** 支付笔数图表 */
  const payNumChartRef = ref<InstanceType<typeof VChart> | null>(null);

  useEcharts([visitChartRef, payNumChartRef]);

  /** 访问量折线图配置 */
  const visitChartOption: EChartsCoreOption = reactive({});

  /** 支付笔数柱状图配置 */
  const payNumChartOption: EChartsCoreOption = reactive({});

  /** 获取支付笔数数据 */
  const getPayNumData = () => {
    getPayNumList()
      .then((data) => {
        Object.assign(visitChartOption, {
          color: '#975fe5',
          tooltip: {
            trigger: 'axis',
            formatter: `<i style="background: #975fe5;
                                  width: 10px;
                                  height: 10px;
                                  margin-right: 5px;
                                  border-radius: 50%;
                                  display: inline-block;"
                        ></i>{b0}: {c0}`
          },
          grid: {
            top: 0,
            bottom: 0,
            left: 0,
            right: 0
          },
          xAxis: [
            {
              show: false,
              type: 'category',
              boundaryGap: false,
              data: data.map((d) => d.date)
            }
          ],
          yAxis: [
            {
              show: false,
              type: 'value',
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              type: 'line',
              smooth: true,
              symbol: 'none',
              areaStyle: {
                opacity: 0.5
              },
              data: data.map((d) => d.value)
            }
          ]
        });

        Object.assign(payNumChartOption, {
          tooltip: {
            trigger: 'axis',
            formatter: `<i style="background: #5b8ff9;
                                  width: 10px;
                                  height: 10px;
                                  margin-right: 5px;
                                  border-radius: 50%;
                                  display: inline-block;"
                        ></i>{b0}: {c0}`
          },
          grid: {
            top: 0,
            bottom: 0,
            left: 0,
            right: 0
          },
          xAxis: [
            {
              show: false,
              type: 'category',
              data: data.map((d) => d.date)
            }
          ],
          yAxis: [
            {
              show: false,
              type: 'value',
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              type: 'bar',
              data: data.map((d) => d.value)
            }
          ]
        });
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  getPayNumData();
</script>

<style lang="scss" scoped>
  .statistics-card {
    :deep(.ele-card-body) {
      padding: 16px 22px 12px 22px;
    }

    :deep(.el-divider) {
      margin: 12px 0;
      opacity: 0.6;
    }

    .statistics-header {
      display: flex;
      align-items: center;

      .statistics-header-text {
        flex: 1;
      }

      .statistics-header-tip {
        font-size: 15px;
        cursor: help;
      }
    }

    .statistics-value {
      margin-top: 4px;
    }

    .statistics-body {
      height: 36px;
      display: flex;
      padding-top: 18px;
      box-sizing: border-box;
    }

    .statistics-trend-text {
      display: flex;
      align-items: center;
      white-space: nowrap;
      word-break: break-all;
      overflow: hidden;

      .el-icon {
        font-size: 16px;
        margin-left: 4px;
      }

      & + .statistics-trend-text {
        margin-left: 14px;
      }
    }

    .statistics-footer {
      display: flex;
      align-items: center;
    }
  }
</style>
