<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="800"
    v-model="visible"
    :title="isUpdate ? '修改日程' : '新增日程'"
    :close-on-click-modal="false"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="日程标题" prop="title">
        <el-input clearable v-model="form.title" placeholder="请输人日程标题" />
      </el-form-item>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="日程日期" prop="planDate">
            <el-date-picker
              v-model="form.planDate"
              clearable
              placeholder="请选择日程日期"
              type="date"
              value-format="x"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="日程时间" prop="planTime">
            <el-time-picker
              v-model="form.planTime"
              placeholder="请选择日程时间"
              clearable
              class="ele-fluid"
              value-format="HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="日程详细" prop="planDetail">
        <el-input
          type="textarea"
          clearable
          v-model="form.planDetail"
          rows="6"
          placeholder="请输入日程详细"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">关闭</el-button>
      <el-button
        v-if="isUpdate"
        type="danger"
        :loading="loading"
        @click="deletePlan"
      >
        删除
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from 'sirius-platform-pro/es';
  import { useFormData } from '@/utils/use-form-data';
  import * as UserPlandApi from '@/api/system/plan';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    title: '',
    planDate: '',
    planTime: '',
    planDetail: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    title: [{ required: true, message: '日程描述不能为空', trigger: 'blur' }],
    planDate: [
      { required: true, message: '日程日期不能为空', trigger: 'blur' }
    ],
    planTime: [
      { required: true, message: '日程时间不能为空', trigger: 'blur' }
    ],
    planDetail: [
      { required: true, message: '日程描述不能为空', trigger: 'blur' }
    ]
  });
  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value
        ? UserPlandApi.updateUserPlan
        : UserPlandApi.createUserPlan;
      try {
        await saveOrUpdate(form);
        EleMessage.success('修改成功');
        handleCancel();
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };
  const deletePlan = async () => {
    loading.value = true;
    try {
      await UserPlandApi.deleteUserPlan(form.id);
      EleMessage.success('修改成功');
      handleCancel();
      emit('done');
    } finally {
      loading.value = false;
    }
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      const res = await UserPlandApi.getUserPlan(props.data.id);
      assignFields(res);
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>
