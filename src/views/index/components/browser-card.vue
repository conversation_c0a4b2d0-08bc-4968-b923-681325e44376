<template>
  <ele-card class="channel-distribution-card">
    <template #header>
      <div class="card-header-custom">
        <el-icon class="header-icon"><Monitor /></el-icon>
        <span class="header-title">发布渠道分布</span>
      </div>
    </template>

    <div class="channel-chart-container">
      <v-chart
        ref="channelChartRef"
        :option="channelChartOption"
        class="channel-chart"
        @click="handleChannelChartClick"
      />
    </div>

    <!-- 渠道统计 -->
    <div class="channel-stats">
      <div class="stat-item" v-for="item in channelData" :key="item.name">
        <div class="stat-dot" :style="{ background: item.color }"></div>
        <div class="stat-info">
          <span class="stat-name">{{ item.name }}</span>
          <span class="stat-value">{{ item.value }}台</span>
        </div>
        <div class="stat-percent">{{ item.percent }}%</div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { EleMessage } from 'sirius-platform-pro';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { PieChart } from 'echarts/charts';
  import { TooltipComponent, LegendComponent } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { Monitor } from '@element-plus/icons-vue';

  use([CanvasRenderer, PieChart, TooltipComponent, LegendComponent]);

  const channelChartRef = ref(null);

  // 发布渠道数据
  const channelData = ref([
    { name: 'LED屏幕', value: 1245, percent: 42.5, color: '#4facfe' },
    { name: 'LCD显示器', value: 896, percent: 30.6, color: '#00d4ff' },
    { name: '投影设备', value: 456, percent: 15.6, color: '#ffd93d' },
    { name: '移动终端', value: 234, percent: 8.0, color: '#ff9f43' },
    { name: '其他设备', value: 96, percent: 3.3, color: '#ff6b6b' }
  ]);

  // 图表配置
  const channelChartOption = reactive({
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}台 ({d}%)',
      backgroundColor: 'rgba(0, 20, 60, 0.9)',
      borderColor: '#00d4ff',
      textStyle: { color: '#ffffff' }
    },
    series: [
      {
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        data: channelData.value.map((item) => ({
          name: item.name,
          value: item.value,
          itemStyle: { color: item.color }
        }))
      }
    ]
  });

  // 图表点击事件
  const handleChannelChartClick = (params) => {
    EleMessage.info(`选中渠道: ${params.name}，设备数量: ${params.value}台`);
  };

  defineOptions({ name: 'ChannelDistributionCard' });
</script>

<style scoped>
  .channel-distribution-card {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      0 0 20px rgba(74, 144, 226, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    overflow: hidden;
  }

  .card-header-custom {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0;
  }

  .header-icon {
    color: #4facfe;
    font-size: 18px;
  }

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
  }

  .channel-chart-container {
    height: 200px;
    margin-bottom: 16px;
  }

  .channel-chart {
    width: 100%;
    height: 100%;
  }

  .channel-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid rgba(74, 172, 254, 0.2);
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(74, 172, 254, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .stat-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(74, 172, 254, 0.3);
    transform: translateX(2px);
  }

  .stat-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
    flex-shrink: 0;
  }

  .stat-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .stat-name {
    font-size: 14px;
    color: #ffffff;
    font-weight: 500;
  }

  .stat-value {
    font-size: 14px;
    color: #4facfe;
    font-weight: 600;
  }

  .stat-percent {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    min-width: 40px;
    text-align: right;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .channel-chart-container {
      height: 160px;
    }

    .stat-item {
      padding: 6px 10px;
      gap: 10px;
    }

    .stat-dot {
      width: 10px;
      height: 10px;
    }

    .stat-name,
    .stat-value {
      font-size: 12px;
    }

    .stat-percent {
      font-size: 11px;
      min-width: 35px;
    }
  }

  @media (max-width: 480px) {
    .channel-chart-container {
      height: 140px;
    }

    .stat-item {
      padding: 5px 8px;
      gap: 8px;
    }

    .stat-dot {
      width: 8px;
      height: 8px;
    }

    .stat-name,
    .stat-value {
      font-size: 11px;
    }

    .stat-percent {
      font-size: 10px;
      min-width: 30px;
    }
  }
</style>
