<template>
  <ele-page flex-table>
    <ele-card
      flex-table
      :body-style="{ paddingTop: '8px' }"
      v-loading="pageLoading"
      element-loading-text="正在加载..."
    >
      <div class="mx-auto">
        <!-- 头部导航栏 -->
        <div class="page-header-nav">
          <!-- 左侧标题区 -->
          <div class="header-left">
            <el-button type="text" class="back-btn" @click="handleBack">
              <Icon icon="ep:arrow-left" class="back-icon" />
            </el-button>
            <div class="title-section">
              <h1 class="page-title" :title="formData.name || '创建流程'">
                {{ formData.name || '创建流程' }}
              </h1>
            </div>
          </div>

          <!-- 步骤条 -->
          <div class="steps-nav-container">
            <div class="steps-nav-inner">
              <div
                v-for="(step, index) in steps"
                :key="index"
                class="step-item"
                :class="[
                  currentStep === index
                    ? 'is-active'
                    : index < currentStep
                      ? 'is-completed'
                      : 'is-upcoming',
                  index <= currentStep || index === currentStep + 1
                    ? 'is-clickable'
                    : ''
                ]"
                @click="handleStepClick(index)"
              >
                <div class="step-badge">{{ index + 1 }}</div>
                <span class="step-label">{{ step.title }}</span>
              </div>
            </div>
          </div>

          <!-- 右侧操作区 -->
          <div class="header-actions">
            <el-button
              v-if="actionType === 'update'"
              type="success"
              class="action-btn"
              @click="handleDeploy"
              :loading="deployLoading"
            >
              <Icon icon="ep:upload" class="btn-icon" />
              发 布
            </el-button>
            <el-button
              type="primary"
              class="action-btn"
              @click="handleSave"
              :loading="saveLoading"
            >
              <Icon
                v-if="actionType === 'definition'"
                icon="ep:refresh"
                class="btn-icon"
              />
              <Icon v-else icon="ep:document" class="btn-icon" />
              <span v-if="actionType === 'definition'">恢 复</span>
              <span v-else>保 存</span>
            </el-button>
          </div>
        </div>

        <!-- 主体内容 -->
        <div class="mt-60px">
          <!-- 第一步：基本信息 -->
          <div v-if="currentStep === 0" class="mx-auto w-560px">
            <BasicInfo
              v-model="formData"
              :categoryList="categoryList"
              :userList="userList"
              :deptList="deptList"
              ref="basicInfoRef"
            />
          </div>

          <!-- 第二步：表单设计 -->
          <div v-if="currentStep === 1" class="mx-auto w-560px">
            <FormDesign
              v-model="formData"
              :formList="formList"
              ref="formDesignRef"
            />
          </div>

          <!-- 第三步：流程设计 -->
          <ProcessDesign
            v-if="currentStep === 2"
            v-model="formData"
            ref="processDesignRef"
          />

          <!-- 第四步：更多设置 -->
          <div v-show="currentStep === 3" class="mx-auto w-700px">
            <ExtraSettings
              ref="extraSettingsRef"
              v-model="formData"
              :model-form-id="formData.formId"
            />
          </div>
        </div>
      </div>
    </ele-card>
  </ele-page>
</template>

<script lang="ts" setup>
  import { useRoute, useRouter } from 'vue-router';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useUserStore } from '@/store/modules/user';
  import * as ModelApi from '@/api/bpm/model';
  import * as FormApi from '@/api/bpm/form';
  import { CategoryApi, CategoryVO } from '@/api/bpm/category';
  import * as UserApi from '@/api/system/user';
  import * as DeptApi from '@/api/system/dept';
  import * as DefinitionApi from '@/api/bpm/definition';
  import { usePageTab } from '@/utils/use-page-tab';

  import {
    BpmModelFormType,
    BpmModelType,
    BpmAutoApproveType
  } from '@/utils/constants';
  import BasicInfo from './BasicInfo.vue';
  import FormDesign from './FormDesign.vue';
  import ProcessDesign from './ProcessDesign.vue';
  import ExtraSettings from './ExtraSettings.vue';
  import {
    watch,
    ref,
    provide,
    nextTick,
    onMounted,
    onBeforeUnmount
  } from 'vue';

  const { routeTabKey, removePageTab, setPageTabTitle } = usePageTab();

  const router = useRouter();
  const route = useRoute();
  const message = useMessage();
  const userStore = useUserStore();

  // 组件引用
  const basicInfoRef = ref();
  const formDesignRef = ref();
  const processDesignRef = ref();
  const extraSettingsRef = ref();

  /** 步骤校验函数 */
  const validateBasic = async () => {
    await basicInfoRef.value?.validate();
  };

  /** 表单设计校验 */
  const validateForm = async () => {
    await formDesignRef.value?.validate();
  };

  /** 流程设计校验 */
  const validateProcess = async () => {
    await processDesignRef.value?.validate();
  };

  const currentStep = ref(-1); // 步骤控制。-1 用于，一开始全部不展示等当前页面数据初始化完成

  // 加载状态
  const pageLoading = ref(false);
  const saveLoading = ref(false);
  const deployLoading = ref(false);

  const steps = [
    { title: '基本信息', validator: validateBasic },
    { title: '表单设计', validator: validateForm },
    { title: '流程设计', validator: validateProcess },
    { title: '更多设置', validator: null }
  ];

  // 表单数据
  const formData: any = ref({
    id: undefined,
    name: '',
    key: '',
    category: undefined,
    icon: undefined,
    description: '',
    type: BpmModelType.BPMN,
    formType: BpmModelFormType.NORMAL,
    formId: '',
    formCustomCreatePath: '',
    formCustomViewPath: '',
    visible: true,
    startUserType: undefined,
    startUserIds: [],
    startDeptIds: [],
    managerUserIds: [],
    allowCancelRunningProcess: true,
    processIdRule: {
      enable: false,
      prefix: '',
      infix: '',
      postfix: '',
      length: 5
    },
    autoApprovalType: BpmAutoApproveType.NONE,
    titleSetting: {
      enable: false,
      title: ''
    },
    summarySetting: {
      enable: false,
      summary: []
    }
  });

  // 流程数据
  const processData = ref<any>();

  provide('processData', processData);
  provide('modelData', formData);

  // 数据列表
  const formList = ref([]);
  const categoryList = ref<CategoryVO[]>([]);
  const userList = ref<UserApi.UserVO[]>([]);
  const deptList = ref<DeptApi.DeptVO[]>([]);

  /** 初始化数据 */
  const actionType = route.params.type as string;
  const initData = async () => {
    pageLoading.value = true;
    try {
      if (actionType === 'definition') {
        // 情况一：流程定义场景（恢复）
        const definitionId = route.params.id as string;
        const data = await DefinitionApi.getProcessDefinition(definitionId);
        // 将 definition => model，最终赋值
        data.type = data.modelType;
        delete data.modelType;
        data.id = data.modelId;
        delete data.modelId;
        if (data.simpleModel) {
          data.simpleModel = JSON.parse(data.simpleModel);
        }
        formData.value = data;
        formData.value.startUserType =
          formData.value.startUserIds?.length > 0
            ? 1
            : formData.value?.startDeptIds?.length > 0
              ? 2
              : 0;
      } else if (['update', 'copy'].includes(actionType)) {
        // 情况二：修改场景/复制场景
        const modelId = route.params.id as string;
        formData.value = await ModelApi.getModel(modelId);
        formData.value.startUserType =
          formData.value.startUserIds?.length > 0
            ? 1
            : formData.value?.startDeptIds?.length > 0
              ? 2
              : 0;

        // 特殊：复制场景
        if (route.params.type === 'copy') {
          delete formData.value.id;
          if (formData.value.bpmnXml) {
            formData.value.bpmnXml = formData.value.bpmnXml.replaceAll(
              formData.value.name,
              formData.value.name + '副本'
            );
            formData.value.bpmnXml = formData.value.bpmnXml.replaceAll(
              formData.value.key,
              formData.value.key + '_copy'
            );
          }
          formData.value.name += '副本';
          formData.value.key += '_copy';
          setPageTabTitle('复制流程');
        }
      } else {
        // 情况三：新增场景
        formData.value.startUserType = 0; // 全体
        formData.value.managerUserIds.push(userStore.getUser.id);
      }

      // 获取表单列表
      formList.value = await FormApi.getFormSimpleList();
      // 获取分类列表
      categoryList.value = await CategoryApi.getCategorySimpleList();
      // 获取用户列表
      userList.value = await UserApi.getSimpleUserList();
      // 获取部门列表
      deptList.value = await DeptApi.getSimpleDeptList();

      // 最终，设置 currentStep 切换到第一步
      currentStep.value = 0;

      // 兼容，以前未配置更多设置的流程
      extraSettingsRef.value.initData();
    } finally {
      pageLoading.value = false;
    }
  };

  /** 根据类型切换流程数据 */
  watch(
    async () => formData.value.type,
    () => {
      if (formData.value.type === BpmModelType.BPMN) {
        processData.value = formData.value.bpmnXml;
      } else if (formData.value.type === BpmModelType.SIMPLE) {
        processData.value = formData.value.simpleModel;
      }
    },
    {
      immediate: true
    }
  );

  /** 校验所有步骤数据是否完整 */
  const validateAllSteps = async () => {
    try {
      // 基本信息校验
      try {
        await validateBasic();
      } catch (error) {
        currentStep.value = 0;
        throw new Error('请完善基本信息');
      }

      // 表单设计校验
      try {
        await validateForm();
      } catch (error) {
        currentStep.value = 1;
        throw new Error('请完善自定义表单信息');
      }

      // 流程设计校验

      // 表单设计校验
      try {
        await validateProcess();
      } catch (error) {
        currentStep.value = 2;
        throw new Error('请设计流程');
      }

      return true;
    } catch (error) {
      throw error;
    }
  };

  /** 保存操作 */
  const handleSave = async () => {
    saveLoading.value = true;
    try {
      // 保存前校验所有步骤的数据
      await validateAllSteps();

      // 更新表单数据
      const modelData = {
        ...formData.value
      };

      if (actionType === 'definition') {
        // 情况一：流程定义场景（恢复）
        await ModelApi.updateModel(modelData);
        // 提示成功
        message.success('恢复成功，可点击【发布】按钮，进行发布模型');
      } else if (actionType === 'update') {
        // 修改场景
        await ModelApi.updateModel(modelData);
        // 提示成功
        message.success('修改成功，可点击【发布】按钮，进行发布模型');
      } else if (actionType === 'copy') {
        // 情况三：复制场景
        formData.value.id = await ModelApi.createModel(modelData);
        // 提示成功
        message.success('复制成功，可点击【发布】按钮，进行发布模型');
      } else {
        // 情况四：新增场景
        formData.value.id = await ModelApi.createModel(modelData);
        // 提示成功
        message.success('新建成功，可点击【发布】按钮，进行发布模型');
      }

      // 返回列表页（排除更新的情况）
      if (actionType !== 'update') {
        await router.push({ name: 'BpmModel' });
      }
    } catch (error: any) {
      console.error('保存失败:', error);
      message.warning(error.message || '请完善所有步骤的必填信息');
    } finally {
      saveLoading.value = false;
    }
  };

  /** 发布操作 */
  const handleDeploy = async () => {
    deployLoading.value = true;
    try {
      // 修改场景下直接发布，新增场景下需要先确认
      if (!formData.value.id) {
        await message.confirm('是否确认发布该流程？');
      }
      // 校验所有步骤
      await validateAllSteps();

      // 更新表单数据
      const modelData = {
        ...formData.value
      };

      // 先保存所有数据
      if (formData.value.id) {
        await ModelApi.updateModel(modelData);
      } else {
        const result = await ModelApi.createModel(modelData);
        formData.value.id = result.id;
      }

      // 发布
      await ModelApi.deployModel(formData.value.id);
      message.success('发布成功');
      // 返回列表页
      await router.push({ name: 'BpmModel' });
    } catch (error: any) {
      console.error('发布失败:', error);
      message.warning(error.message || '发布失败');
    } finally {
      deployLoading.value = false;
    }
  };

  /** 步骤切换处理 */
  const handleStepClick = async (index: number) => {
    try {
      if (index !== 0) {
        await validateBasic();
      }
      if (index !== 1) {
        await validateForm();
      }
      if (index !== 2) {
        await validateProcess();
      }

      // 切换步骤
      currentStep.value = index;

      // 如果切换到流程设计步骤，等待组件渲染完成后刷新设计器
      if (index === 2) {
        await nextTick();
        // 等待更长时间确保组件完全初始化
        await new Promise((resolve) => setTimeout(resolve, 200));
        if (processDesignRef.value?.refresh) {
          await processDesignRef.value.refresh();
        }
      }
    } catch (error) {
      console.error('步骤切换失败:', error);
      message.warning('请先完善当前步骤必填信息');
    }
  };

  /** 返回列表页 */
  const handleBack = () => {
    // 先删除当前页签
    removePageTab({ key: routeTabKey });
    // 跳转到列表页
    router.push('/bpm/manager/model');
  };

  /** 初始化 */
  onMounted(async () => {
    await initData();
  });

  // 添加组件卸载前的清理代码
  onBeforeUnmount(() => {
    // 清理所有的引用
    basicInfoRef.value = null;
    formDesignRef.value = null;
    processDesignRef.value = null;
  });
</script>

<style lang="scss" scoped>
  .border-bottom {
    border-bottom: 1px solid #e5e7eb; /* 更柔和的分隔线 */
  }

  /* 头部导航栏样式 - 简洁现代风格 */
  .page-header-nav {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    z-index: 10;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  }

  .header-left {
    display: flex;
    align-items: center;
    width: 280px;
    min-width: 0;
  }

  .back-btn {
    padding: 8px;
    margin-right: 16px;
    border-radius: 6px;
    background: transparent;
    border: none;
    color: #6b7280;
    transition: all 0.2s ease;
  }

  .back-btn:hover {
    background: #f3f4f6;
    color: #374151;
  }

  .back-icon {
    font-size: 18px;
  }

  .title-section {
    display: flex;
    flex-direction: column;
    min-width: 0;
  }

  .page-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 2px 0;
    line-height: 1.3;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .page-meta {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 280px;
    justify-content: flex-end;
  }

  /* 步骤条居中布局 */
  .steps-nav-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  .steps-nav-inner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 32px;
    height: 100%;
  }

  /* 步骤条样式 */
  .step-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    position: relative;
  }

  .step-item.is-clickable:hover {
    background: #f3f4f6;
    transform: translateY(-1px);
  }

  /* 圆形序号徽章 */
  .step-badge {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 12px;
    font-weight: 600;
    border: 2px solid #d1d5db;
    color: #6b7280;
    background: #fff;
    transition: all 0.2s ease;
  }

  /* 文本标签 */
  .step-label {
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    transition: color 0.2s ease;
    white-space: nowrap;
  }

  /* 激活态 */
  .step-item.is-active {
    color: #2563eb;
  }

  .step-item.is-active .step-badge {
    background: #2563eb;
    color: #fff;
    border-color: #2563eb;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.25);
  }

  .step-item.is-active .step-label {
    color: #2563eb;
    font-weight: 600;
  }

  /* 已完成态 */
  .step-item.is-completed {
    color: #10b981;
  }

  .step-item.is-completed .step-badge {
    background: #10b981;
    color: #fff;
    border-color: #10b981;
    box-shadow: 0 2px 6px rgba(16, 185, 129, 0.2);
  }

  .step-item.is-completed .step-label {
    color: #10b981;
    font-weight: 500;
  }

  /* 未到达态 */
  .step-item.is-upcoming .step-badge {
    background: #fff;
    color: #9ca3af;
    border-color: #e5e7eb;
  }

  .step-item.is-upcoming .step-label {
    color: #9ca3af;
  }

  .action-btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    height: 36px;
  }

  .action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .btn-icon {
    margin-right: 4px;
    font-size: 14px;
  }

  /* 顶部步骤条（不改动逻辑，只做视觉优化） */
  .steps-nav {
    .step-item {
      transition:
        transform 0.2s ease,
        color 0.2s ease;

      &.is-clickable:hover {
        transform: translateY(-1px);
      }

      /* 圆形序号徽章 */
      .step-badge {
        width: 26px;
        height: 26px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        font-size: 13px;
        font-weight: 600;
        border: 2px solid #d1d5db; /* 默认灰边 */
        color: #6b7280; /* 字体灰 */
        background: #fff;
        transition: all 0.2s ease;
      }

      /* 文本标签 */
      .step-label {
        font-size: 14px;
        font-weight: 600;
        color: #6b7280;
        transition: color 0.2s ease;
      }

      /* 激活态 */
      &.is-active {
        color: #2563eb;
        border-bottom: 2px solid #2563eb;

        .step-badge {
          background: #2563eb;
          color: #fff;
          border-color: #2563eb;
          box-shadow: 0 4px 10px rgba(37, 99, 235, 0.25);
        }

        .step-label {
          color: #2563eb;
        }
      }

      /* 已完成态 */
      &.is-completed {
        color: #10b981;

        .step-badge {
          background: #10b981;
          color: #fff;
          border-color: #10b981;
          box-shadow: 0 3px 8px rgba(16, 185, 129, 0.22);
        }

        .step-label {
          color: #10b981;
        }
      }

      /* 未到达态 */
      &.is-upcoming {
        .step-badge {
          background: #fff;
          color: #9ca3af;
          border-color: #e5e7eb;
        }

        .step-label {
          color: #9ca3af;
        }
      }
    }
  }
</style>
