import { createApp } from 'vue';
import App from './App.vue';
import store from './store';
import router from './router';
import permission from './utils/permission';

import i18n from './i18n';
// 导入全局的svg图标
import '@/plugins/svgIcon';
import installer from './as-needed';
import { iconsInstaller } from '@/components/IconSelect/util';
import 'element-plus/theme-chalk/display.css';
import 'sirius-platform-pro/es/style/nprogress.scss';
import './styles/themes/rounded.scss';
import './styles/themes/dark.scss';
import './styles/themes/transparent.scss';
import './styles/index.scss';
import { setupFormCreate } from '@/plugins/formCreate';
import '@/plugins/unocss';
import VueDOMPurifyHTML from 'vue-dompurify-html'; // 解决v-html 的安全隐患
// 全局组件
import { setupGlobCom } from '@/components';
const app = createApp(App);
setupFormCreate(app);
setupGlobCom(app);
app.use(store);
app.use(router);
app.use(permission);
app.use(i18n);
app.use(installer);
app.use(iconsInstaller);
app.use(VueDOMPurifyHTML);
app.mount('#app');
