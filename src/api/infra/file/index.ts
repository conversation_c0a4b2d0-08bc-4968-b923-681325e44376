import request from '@/utils/request/server';
import type { AxiosRequestConfig } from 'axios';

// 查询文件列表
export const getFilePage = (params) => {
  return request.get({ url: '/infra/file/page', params });
};

// 删除文件
export const deleteFile = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id });
};

// 获取文件预签名地址
export const getFilePresignedUrl = (path: string) => {
  return request.get({
    url: '/infra/file/presigned-url',
    params: { path }
  });
};

// 创建文件
export const createFile = (data: any) => {
  return request.post({ url: '/infra/file/create', data });
};

// 上传文件
export const uploadFile = async (
  file: File,
  config?: AxiosRequestConfig & {
    onUploadProgress?: (progressEvent: any) => void;
  },
  fileName?: string
) => {
  const formData = new FormData();
  formData.append('file', file, fileName);

  // 将 onUploadProgress 从 config 中提取出来，传递给底层请求
  const { onUploadProgress, ...restConfig } = config || {};

  return request.upload({
    url: '/infra/file/upload-file',
    data: formData, // 使用 data 而不是 formData
    onUploadProgress, // 直接传递进度回调
    ...restConfig
  });
};

// 上传文件
export const updateFileNew = (data: any) => {
  return request.upload({ url: '/infra/file/upload-file', data });
};
